/* Temel form stilleri */
.step {
    display: none;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
    padding: 5px;
    margin-bottom: 20px;
}

.step.active {
    display: block;
}

/* <PERSON><PERSON><PERSON><PERSON> i<PERSON><PERSON> stiller */
.steps-container {
    position: relative;
    padding: 20px 0;
}

.steps {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    margin-bottom: 30px;
}

.step-item {
    text-align: center;
    position: relative;
    z-index: 2;
}

.step-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #fff;
    border: 2px solid #dee2e6;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 10px;
    font-weight: bold;
    transition: all 0.3s ease;
}

.step-title {
    color: #6c757d;
    font-size: 11px;
    font-weight: 500;
    margin-top: 5px;
    transition: all 0.3s ease;
}

.step-item.active .step-circle {
    background-color: #0d6efd;
    border-color: #0d6efd;
    color: #fff;
}

.step-item.active .step-title {
    color: #0d6efd;
    font-weight: 600;
}

.step-item.completed .step-circle {
    background-color: #198754;
    border-color: #198754;
    color: #fff;
}

.step-item.completed .step-title {
    color: #198754;
}

/* Progress bar stilleri */
.progress {
    height: 4px;
    background-color: #dee2e6;
    position: absolute;
    top: 40px;
    left: 0;
    right: 0;
    margin: 0 50px;
    z-index: 1;
}

.progress-bar {
    background-color: #0d6efd;
    transition: width 0.5s ease;
}

/* Hisse butonları */
.hisse-button {
    width: max-content;
    min-height: 5rem;
    margin: 10px 2px 10px 0;
    font-size: 20px;
    border-radius: 15px;
    transition: all 0.3s ease;
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.hisse-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Form alanları */
.form-label {
   
    margin-bottom: 0;
}
.form-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.form-group {
    margin-bottom: 5px;
}

.form-control {
    border-radius: 8px;
    padding: 12px;
    border: 2px solid #dee2e6;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Validasyon stilleri */
.input-error {
    border-color: #dc3545 !important;
}

.input-success {
    border-color: #198754 !important;
}

.error-message {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
    display: none;
}

.form-control:focus.input-error {
    box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25);
}

.form-control:focus.input-success {
    box-shadow: 0 0 0 0.25rem rgba(25, 135, 84, 0.25);
}

/* Başlıklar */
.step h3 {
    color: #2c3e50;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid #eee;
}

/* Butonlar */
.btn {
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
    width: max-content;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Modal stilleri */
.modal-content {
    border-radius: 15px;
    border: none;
}

.modal-header {
    border-bottom: 2px solid #eee;
    padding: 20px;
}

.modal-body {
    padding: 25px;
}

.modal-footer {
    border-top: 2px solid #eee;
    padding: 20px;
}

.copy-button:active {
    transform: scale(0.98);
}

/* Toplam Tutar kartı için stiller */
.total-amount-card {
    background: linear-gradient(135deg, #0d6efd70, #003f9b);
    border-radius: 15px;
    padding: 20px 30px;
    color: white;
    box-shadow: 0 4px 15px rgba(13, 110, 253, 0.2);
    text-align: center;
    transition: transform 0.3s ease;
}

.total-amount-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(13, 110, 253, 0.3);
}

.amount-label {
    font-size: 1.1rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    opacity: 0.9;
    margin-bottom: 10px;
}

.amount-value {
    font-size: 2.5rem;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.amount-value .currency {
    font-size: 1.8rem;
    font-weight: 500;
    opacity: 0.9;
}

/* Animasyon ekleyelim */
@keyframes countAnimation {
    from {
        transform: scale(1);
    }

    50% {
        transform: scale(1.1);
    }

    to {
        transform: scale(1);
    }
}

/* JavaScript'te toplam tutar güncellendiğinde animasyon için */
.amount-update {
    animation: countAnimation 0.3s ease-out;
}
/* Kurban türü seçimi */
.kurban-turu-group {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: stretch;
   
    width: 100%;
    overflow-x: auto;
}

.kurban-turu-card {
    border-radius: 8px;
    border: 2px solid #e0e0e0;
    padding: 18px 8px;
    text-align: center;
    cursor: pointer;
    transition: border-color 0.2s, box-shadow 0.2s;
    position: relative;
    background: #fff;
    min-height: 120px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.kurban-turu-card .kurban-radio {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    margin: 0;
    cursor: pointer;
    z-index: 2;
}

.kurban-turu-card .bx {
    font-size: 2.2rem;
    margin-bottom: 8px;
}

.kurban-turu-card input[type="radio"]:checked ~ i,
.kurban-turu-card input[type="radio"]:checked ~ span {
    color: #0d6efd;
    font-weight: bold;
}

.kurban-turu-card input[type="radio"]:checked ~ .bx {
    color: #0d6efd;
}

.kurban-turu-card input[type="radio"]:checked ~ span {
    color: #0d6efd;
}

.kurban-turu-card:hover {
    border-color: #0d6efd;
    background: #e7f1ff;
    box-shadow: 0 4px 16px rgba(13,110,253,0.08);
}
.KurbanTurBaslik{
    line-height: 15px;
    margin-top: 15px;
}
@media (min-width: 768px) {
    .kurban-turu-group {
        flex-wrap: wrap;
    }
    .kurban-turu-card {
        flex: 1 1 0;
       
        min-width: 120px;
        height: 100%;
    }
}

@media (max-width: 767.98px) {
    .kurban-turu-group {
        flex-wrap: wrap;
        justify-content: center;
        overflow-x: visible;
    }
    .kurban-turu-card {
        width: 100%;
        margin-bottom: 10px;
    }
}

/* Modern Checkbox */
.form-check-input {
    width: 1.5em;
    height: 1.5em;
    border-radius: 0.35em;
    border: 2px solid #0d6efd;
    background-color: #f8f9fa;
    transition: border-color 0.2s, box-shadow 0.2s;
    box-shadow: 0 2px 8px rgba(13,110,253,0.07);
    margin-top: 0.2em;
    cursor: pointer;
}

.form-check-input:checked {
    background-color: #0d6efd;
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.15rem rgba(13,110,253,.15);
}

.form-check-input:focus {
    border-color: #0a58ca;
    outline: none;
    box-shadow: 0 0 0 0.2rem rgba(13,110,253,.15);
}

.form-check-label {
    font-size: 1.08rem;
    font-weight: 500;
    color: #222;
    margin-left: 0.5em;
    cursor: pointer;
    user-select: none;
}

.kurban-radio {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    margin: 0;
    cursor: pointer;
    z-index: 2;
}

.kurban-turu-card {
    border-radius: 8px;
    cursor: pointer;
    position: relative;
}

.kurban-turu-card input:checked + i,
.kurban-turu-card input:checked + span {
    color: #0d6efd;
    font-weight: bold;
}

/* SweetAlert özel stil */
.swal-wide {
    width: 600px !important;
    max-width: 90% !important;
}

.swal-wide .swal2-html-container {
    white-space: pre-line !important;
    text-align: left !important;
    font-size: 14px !important;
    line-height: 1.5 !important;
}