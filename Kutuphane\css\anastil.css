/* ---------------------------------------------------
    GENEL STİLLER
----------------------------------------------------- */

body {
    background-color: #f2f7ff;
    font-family: 'Poppins', Helvetica;
    font-weight: 400; /* Regular */
    font-size: medium;
    min-width: 320px; /* 300 yerine genelde 320px önerilir */
   
  }
  
  .sayfabasligi{
    margin-bottom:1px;
    background-color: #e7f1f7;
  }
  
  p {
    font-family: "Poppins", Arial, Helvetica, sans-serif;
    font-size: 1em;
    font-weight: 300;
    line-height: 1.3em;
    color: #999;
  }
  
  a {
    color: inherit;
    text-decoration: none;
    transition: all 0.3s;
  }
  a:hover {

    text-decoration: none;
    transition: all 0.3s;
    color: #407c32
  }
  a:focus {

    text-decoration: none;
    transition: all 0.3s;
    color: #407c32
  }
  
  a[aria-expanded=true] {
    background: #fff;
  }
  a[aria-expanded=true]:after {
    transform: translateY(-50%) rotate(0deg) !important;
  }
  
  a[data-toggle=collapse] {
    position: relative;
  }
  
  .SayfaBolum{
    padding-bottom: 1.5rem;
    }
  .SayfaBolum .BolumBaslik{
    font-size: 2.1rem;
    line-height: 2.3rem;
    font-family: "Quicksand", Helvetica,Arial, sans-serif;
    font-weight: 400;
    margin-bottom: 1rem;
  }
  .SayfaBolum .BolumAltBaslik{
    font-size: 1.3rem;
    line-height: 1.5rem;
    font-family: "Charm", Helvetica,Arial, sans-serif;
    font-weight: 300;
    margin-bottom: 1rem;
    color: #999;
    
  }
  .hr2 {
    border: 0;
    height:  0.15em;
    background-image: linear-gradient(to right, rgba(0, 0, 0, 0), rgba(9, 84, 132), rgba(0, 0, 0, 0));
    width: 60%;
  }

.golge{ 
    display: flex;
    height: 100%;
    width: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    position: absolute;
    justify-content: center;
    align-items: center;
    transition: opacity 0.4s ease-in-out;
    opacity: 0;
  }
.golge:hover{ 
    opacity: 1;
  }
.golge i { 
  color: #fff;
}
.golge span{ 
  color: #fff;
}
   



/* ---------------------------------------------------
    HEADER ALANI İÇİN KULLANILAN STİLLER
----------------------------------------------------- */
.header-arkaplan{
  background-image: url("../../Depolar/DepoResim/afrika-kurban-3.jpg");
  background-size: cover;
  background-position: center center;
  background-attachment: scroll;
  background-repeat: no-repeat;
  /* height: 100vh; */
}
.header-arkaplan::before{
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.493);
 
}
.header-baslik{
  color: #fff;
  font-size: 2.5rem;
  line-height: 2.5rem;
  font-family: "Charm", Helvetica;
  font-weight: 400;
  margin-bottom: 1rem;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 25rem 0 25rem 0;
  display: inline-block; /* Sadece yazının uzunluğu kadar arka plan için */
  padding: 1rem 2rem 1rem 2rem; 
  clear: both;
  z-index: 1; /* Diğer içeriklerin üstünde görünmesi için */
}

.header-altbaslik{
  color: #000000;
  font-size: 1.2rem;
  line-height: 1.2rem;
  font-family: "Quicksand", Helvetica;
  font-weight: 600;
  margin-bottom: 1rem;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 15rem 15rem 15rem 15rem;
  display: inline-block; /* Sadece yazının uzunluğu kadar arka plan için */
  padding: 0.6rem 1.2rem 0.6rem 1.2rem;
  clear: both; 
  z-index: 1; /* Diğer içeriklerin üstünde görünmesi için */
}

/* ---------------------------------------------------
    Faaliyetlerimiz ALANI İÇİN KULLANILAN STİLLER
----------------------------------------------------- */
#Faaliyetlerimiz .card{
  border: none;
  border-radius: 20px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 1.5rem;
  overflow: hidden;
  
}
#Faaliyetlerimiz .kopru{
  position: relative;;
}
#Faaliyetlerimiz .card .card-body .card-title{
  font-size: 1.2rem;
  font-family: "Quicksand", Helvetica;
  font-weight: 600;
  color: #283a63;
}
#Faaliyetlerimiz .card-img-top{
  width: 100%;
  height: 150px;
  object-fit: cover;
  
}

#Faaliyetlerimiz .golge{ 
  display: flex;
  height: 100%;
  width: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  position: absolute;
  justify-content: center;
  align-items: center;
  transition: opacity 0.4s ease-in-out;
  opacity: 0;
}
#Faaliyetlerimiz .golge:hover{ 
  opacity: 1;
}
#Faaliyetlerimiz .golge i{
  color: #fff;
}




/* ---------------------------------------------------
    Bağış Yap ALANI İÇİN KULLANILAN STİLLER
----------------------------------------------------- */

.tabmenu.nav-tabs {
  border-bottom:2px solid #407c3234;
  display: flex;
  justify-content: center;
  margin-bottom: 0.1rem;
}

.tabmenu .nav-item {
  position: relative;
  overflow: hidden;
}
 .tabmenu .nav-item .TabBaslik{
  margin-bottom: -2px;
  padding: 2rem 3rem 2rem 3rem;
  border-width: 2px;
  font-size: 1rem;
  font-family: "Quicksand", Helvetica;
  font-weight: 700;
  text-align: center;
}

.nav-item .img-container {
  position: relative;
  display: flex;
  justify-content: center;
}

.nav-item .overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.75);
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.nav-item:hover .overlay {
  opacity: 1;
}

.tabmenu .nav-item.show .nav-link,
.tabmenu .nav-link.active {
  color: #495057;
  background-color: #f8f9fa;
  border-width: 2px;
  border-color: #407c3234 #407c3234 #f8f9fa;
}

.tabmenu .nav-link:focus,
.tabmenu .nav-link:hover {
  color: #fff;
  border-color: #407c3234 #407c3234 #f8f9fa;
}

#BagisYap .Tabicerik{
  padding: 1rem;
  border: 0 2px 2px 2px solid #2b532234;
  border-bottom-left-radius: 20px;
  border-bottom-right-radius: 20px;
  background-color: #f8f9fa;
  box-shadow: 0 4px 8px #2b532234
}

#BagisYap .TabicerikAlani{
color: #283a63;
font-family: "Quicksand", Helvetica;

}

#BagisYap .BagisYapResim{
 object-fit: cover;
}

/* ---------------------------------------------------
    Sayfa Üst Çerçeve İÇİN KULLANILAN STİLLER
----------------------------------------------------- */

.SayfaUstCerceve {
  height: 220px;
  position: relative;
  overflow: hidden;
}
.SayfaUstGolge{
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5); /* yarı saydam siyah */
  z-index: -1; /* resmin üstünde, metnin altında */
  display: flex;
  justify-content: center;  /* yatay ortalama */
  align-items: flex-end;     /* dikeyde aşağıya yaslama */
  padding-bottom: 20px;      /* aşağıdan biraz iç boşluk */
  
}
.SayfaBaslik {
  color: white;
  font-size: 1.5rem;
  font-weight: bold;
  text-align: center;
  font-family: "Quicksand", Helvetica,Arial, sans-serif;
}
.SayfaSlogan{
  font-family: 'Charm', Helvetica, Arial, sans-serif;
  font-size: 1.5rem;
  color: #cecccc;
}

.SayfaUstResim {
  width: 100%;
  height: 100%;
  object-fit: cover;
  position: absolute;
  top: 0;
  left: 0;
  z-index: -2; /* en arkada */
}

/* ---------------------------------------------------
    BS5 ÖZELLEŞTİRİLEN STİLLER
----------------------------------------------------- */

/****** Navbar - #ustmenu *****/
#ustmenualan{
  background-color: #0344697a;
  border: none;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  border-radius: 17px;
  margin:15px;
}
/* Üst Menü Logo için Stil Ayarları */
#ustmenualan .navbar-brand{
  font-size: 1.5rem;
  font-family: "Charm", Helvetica;
  font-weight: 400;
  letter-spacing: 0.063em;
  color: #fff !important;
}

#ustmenualan .navbar-nav .nav-item .nav-link{
  font-weight: 600;
  font-family: "Quicksand", Helvetica;
  color: #283a63;
  letter-spacing: 0.06em;
  position: relative;
} 

#ustmenualan .navbar-nav .nav-item .nav-link.active,
#ustmenualan .navbar-nav .nav-item .nav-link:hover{
    color: #407c32
 }

 #ustmenualan .navbar-nav .nav-item .nav-link.active::before,
 #ustmenualan .navbar-nav .nav-item .nav-link:hover::before{
  
     width: 80%;
     visibility: visible;
  }

 #ustmenualan .navbar-nav .nav-item .nav-link::before{
      content:"";
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 0;
      height: 2px;
      background-color: #407c32;
      visibility: hidden;
      transition: all 0.3s ease-in-out 0s;
  }

#ustmenualan .navbar-toggler:focus{
    outline: none;
    box-shadow: #407c3277 0 0 0 0.2rem;
    border: none;
  
}



