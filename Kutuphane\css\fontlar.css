/************ Charm Fontu *********************/
@font-face {
  font-family: 'Charm'; 
  src: local('Charm Regular'), local('Charm-Regular'),
       url('../fontlar/Charm/Charm-Regular.ttf') format('truetype');
  font-weight: 400; /* Regular */
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Charm'; 
  src: local('Charm Bold'), local('Charm-Bold'),
       url('../fontlar/Charm/Charm-Bold.ttf') format('truetype');
  font-weight: 700; /* Bold */
  font-style: normal;
  font-display: swap;
}

/************ Quicksand Fontu *********************/

@font-face {
  font-family: 'Quicksand';
  src: local('Quicksand Variable'), local('Quicksand-Variable'),
       url('../fontlar/Quicksand/Quicksand-VariableFont_wght.ttf') format('truetype');
  font-weight: 100 900; /* Light */
  font-style: normal;
  font-display: swap;
}


/******** Montserrat Variable Font **************/
@font-face {
  font-family: 'Montserrat';
  src: local('Montserrat Variable'), local('Montserrat-Variable'),
       url('../fontlar/Montserrat/Montserrat-VariableFont_wght.ttf') format('truetype');
  font-weight: 100 900; /* Tüm ağırlıkları kapsar */
  font-style: normal;
  font-display: swap;
}

/********** Montserrat Italic Variable Font ************/
@font-face {
  font-family: 'Montserrat';
  src: local('Montserrat Italic Variable'), local('Montserrat-Italic-Variable'),
       url('../fontlar/Montserrat/Montserrat-Italic-VariableFont_wght.ttf') format('truetype');
  font-weight: 100 900;
  font-style: italic;
  font-display: swap;
}

/********** Nunito Variable Font ************/
@font-face {
  font-family: 'Nunito';
  src: local('Nunito Variable'), local('Nunito-Variable'),
       url('../fontlar/Nunito/Nunito-VariableFont_wght.ttf') format('truetype');
  font-weight: 100 900;
  font-style: normal;
  font-display: swap;
}

/********** Nunito Italic Variable Font ************/
@font-face {
  font-family: 'Nunito';
  src: local('Nunito Italic Variable'), local('Nunito-Italic-Variable'),
       url('../fontlar/Nunito/Nunito-Italic-VariableFont_wght.ttf') format('truetype');
  font-weight: 100 900;
  font-style: italic;
  font-display: swap;
}

/* Poppins Thin */
@font-face {
  font-family: 'Poppins';
  src: local('Poppins Thin'), local('Poppins-Thin'),
       url('../fontlar/Poppins/Poppins-Thin.ttf') format('truetype');
  font-weight: 100;
  font-style: normal;
  font-display: swap;
}

/* Poppins Thin Italic */
@font-face {
  font-family: 'Poppins';
  src: local('Poppins Thin Italic'), local('Poppins-ThinItalic'),
       url('../fontlar/Poppins/Poppins-ThinItalic.ttf') format('truetype');
  font-weight: 100;
  font-style: italic;
  font-display: swap;
}

/* Poppins Extra Light */
@font-face {
  font-family: 'Poppins';
  src: local('Poppins ExtraLight'), local('Poppins-ExtraLight'),
       url('../fontlar/Poppins/Poppins-ExtraLight.ttf') format('truetype');
  font-weight: 200;
  font-style: normal;
  font-display: swap;
}

/* Poppins Extra Light Italic */
@font-face {
  font-family: 'Poppins';
  src: local('Poppins ExtraLight Italic'), local('Poppins-ExtraLightItalic'),
       url('../fontlar/Poppins/Poppins-ExtraLightItalic.ttf') format('truetype');
  font-weight: 200;
  font-style: italic;
  font-display: swap;
}

/* Poppins Light */
@font-face {
  font-family: 'Poppins';
  src: local('Poppins Light'), local('Poppins-Light'),
       url('../fontlar/Poppins/Poppins-Light.ttf') format('truetype');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

/* Poppins Light Italic */
@font-face {
  font-family: 'Poppins';
  src: local('Poppins Light Italic'), local('Poppins-LightItalic'),
       url('../fontlar/Poppins/Poppins-LightItalic.ttf') format('truetype');
  font-weight: 300;
  font-style: italic;
  font-display: swap;
}

/* Poppins Regular */
@font-face {
  font-family: 'Poppins';
  src: local('Poppins Regular'), local('Poppins-Regular'),
       url('../fontlar/Poppins/Poppins-Regular.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

/* Poppins Italic */
@font-face {
  font-family: 'Poppins';
  src: local('Poppins Italic'), local('Poppins-Italic'),
       url('../fontlar/Poppins/Poppins-Italic.ttf') format('truetype');
  font-weight: 400;
  font-style: italic;
  font-display: swap;
}

/* Poppins Medium */
@font-face {
  font-family: 'Poppins';
  src: local('Poppins Medium'), local('Poppins-Medium'),
       url('../fontlar/Poppins/Poppins-Medium.ttf') format('truetype');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

/* Poppins Medium Italic */
@font-face {
  font-family: 'Poppins';
  src: local('Poppins Medium Italic'), local('Poppins-MediumItalic'),
       url('../fontlar/Poppins/Poppins-MediumItalic.ttf') format('truetype');
  font-weight: 500;
  font-style: italic;
  font-display: swap;
}

/* Poppins Semi Bold */
@font-face {
  font-family: 'Poppins';
  src: local('Poppins SemiBold'), local('Poppins-SemiBold'),
       url('../fontlar/Poppins/Poppins-SemiBold.ttf') format('truetype');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

/* Poppins Semi Bold Italic */
@font-face {
  font-family: 'Poppins';
  src: local('Poppins SemiBold Italic'), local('Poppins-SemiBoldItalic'),
       url('../fontlar/Poppins/Poppins-SemiBoldItalic.ttf') format('truetype');
  font-weight: 600;
  font-style: italic;
  font-display: swap;
}

/* Poppins Bold */
@font-face {
  font-family: 'Poppins';
  src: local('Poppins Bold'), local('Poppins-Bold'),
       url('../fontlar/Poppins/Poppins-Bold.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

/* Poppins Bold Italic */
@font-face {
  font-family: 'Poppins';
  src: local('Poppins Bold Italic'), local('Poppins-BoldItalic'),
       url('../fontlar/Poppins/Poppins-BoldItalic.ttf') format('truetype');
  font-weight: 700;
  font-style: italic;
  font-display: swap;
}

/* Poppins Extra Bold */
@font-face {
  font-family: 'Poppins';
  src: local('Poppins ExtraBold'), local('Poppins-ExtraBold'),
       url('../fontlar/Poppins/Poppins-ExtraBold.ttf') format('truetype');
  font-weight: 800;
  font-style: normal;
  font-display: swap;
}

/* Poppins Extra Bold Italic */
@font-face {
  font-family: 'Poppins';
  src: local('Poppins ExtraBold Italic'), local('Poppins-ExtraBoldItalic'),
       url('../fontlar/Poppins/Poppins-ExtraBoldItalic.ttf') format('truetype');
  font-weight: 800;
  font-style: italic;
  font-display: swap;
}

/* Poppins Black */
@font-face {
  font-family: 'Poppins';
  src: local('Poppins Black'), local('Poppins-Black'),
       url('../fontlar/Poppins/Poppins-Black.ttf') format('truetype');
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}

/* Poppins Black Italic */
@font-face {
  font-family: 'Poppins';
  src: local('Poppins Black Italic'), local('Poppins-BlackItalic'),
       url('../fontlar/Poppins/Poppins-BlackItalic.ttf') format('truetype');
  font-weight: 900;
  font-style: italic;
  font-display: swap;
}
  
 