.hr1 {
    border: 0;
    height: 0.15em;
    background: #095484;
    background-image: linear-gradient(to right, #ccc, #095484, #ccc);
  }
  .hr2 {
    border: 0;
    height:  0.25em;
    background-image: linear-gradient(to right, rgba(0, 0, 0, 0), rgba(9, 84, 132), rgba(0, 0, 0, 0));
  }

  .hr3 {
    height: 15px;
    border: 0;
    box-shadow: inset 0 12px 12px -12px rgba(9, 84, 132);
  }
  .hr4 {
    border-top: 3px double #095484;
  }

  .hr5 {
    background-color: #fff;
    border-top: 2px dashed #095484;
  }

  .hr6 {
    border-top: 2px dotted #095484;
   }

   .hr7 {
    height: 30px;
    border-style: solid;
    border-color: #095484;
    border-width: 1px 0 0 0;
    border-radius: 20px;
  }

  .hr8 {
    display: block;
    content: "";
    height: 30px;
    margin-top: -31px;
    border-style: solid;
    border-color: #095484;
    border-width: 0 0 1px 0;
    border-radius: 20px;
  }


/* <PERSON><PERSON><PERSON><PERSON>*/

.hrResim1 {
    height: 25px;
    background: url(resimler/sperator-1.png) no-repeat center;
    border: none;
  }
  .hrResim2 {
    height: 25px;
    background: url(resimler/sperator-2.png) no-repeat center;
    border: none;
  }
  .hrResim3 {
    height: 25px;
    background: url(resimler/sperator-3.png) no-repeat center;
    border: none;
  }
  .hrResim4 {
    height: 20px;
    background: url(resimler/sperator-4.png) no-repeat center;
    border: none;
  }
  .hrResim5 {
    height: 50px;
    background: url(resimler/sperator-5.png) no-repeat center;
    border: none;
  }
  .hrResim6 {
    height: 70px;
    background: url(resimler/sperator-6.png) no-repeat center;
    border: none;
  }
  .hrResim7 {
    height: 70px;
    background: url(resimler/sperator-7.png) no-repeat center;
    border: none;
  }
  .hrResim8 {
    height: 70px;
    background: url(resimler/sperator-8.png) no-repeat center;
    border: none;
  }
