/*  Small devices (landscape phones, 576px and up) */
@media (min-width: 576px) { 
}

/*  Medium devices (tablets, 768px and up) */
@media (min-width: 768px) { 
}

/* Large devices (desktops, 992px and up) */
@media (min-width: 992px) { 
    .header-arkaplan{
        padding-top: 15rem;
        padding-bottom: 15rem;
    }

    #ustmenualan {
        color: rgb(51, 98, 151);
        margin: 25px;
        border-radius: 17px;
        padding: 5px;
       
    } 
    .offcanvas-body{
        background-color: none;
    }
   #ustmenualan .navbar-brand>img{
    width: 30%;
    }
   
   #ustmenualan.ust-sabitle{
   /*ust-sabitle özelliği js ile aktif hale geitirliyor. Ekran kaydırma işleminde çalışır. */
     border-radius: 0;
     margin:0;
     padding: 0;  
     box-shadow: 25rem #407c329d;
   } 

   #BagisYap .BagisYapResim{
    height: 370px;
    object-fit: cover;
} 
.SayfaUstCerceve {
    height: 300px;
    position: relative;
    overflow: hidden;
  }

}

/*  X-Large devices (large desktops, 1200px and up) */
@media (min-width: 1200px) {  }

/*  XX-Large devices (larger desktops, 1400px and up) */
@media (min-width: 1400px) {  }

/* ******************************************************** */

/* `sm` applies to x-small devices (portrait phones, less than 576px) */
@media (max-width: 575.98px) {  

    #ustmenualan .navbar-brand>img{
        width: 15%;
    
        }
}

/*  `md` applies to small devices (landscape phones, less than 768px) */
@media (max-width: 767.98px) { 

   
}

/*  `lg` applies to medium devices (tablets, less than 992px) */
@media (max-width: 991.98px) { 
    .header-arkaplan{
        padding-top: 13rem;
        padding-bottom: 11rem;
    }
    .header-baslik{
        font-size: 1.5rem;
    }
    .header-altbaslik{
        font-size: 0.8rem;
    }
    #ustmenualan .navbar-brand>img{
        width: 60%;
        }
    #ustmenualan .navbar-brand{
            width: 50%;
            }

     #ustmenualan .navbar-nav .nav-item .nav-link.active::before,
     #ustmenualan .navbar-nav .nav-item .nav-link:hover::before{
            
            width: 50%;
            visibility: visible;
            height: 1px;
            background-color: #407c329d;
             }

     #ustmenualan .navbar-nav .nav-item .nav-link::before{
        transform: translateX(-105%);
        transition: all 0.3s ease-in-out 0s;
        height: 1px;
            background-color: #407c329d;
        }

    #BagisYap .BagisYapResim{
        max-height: 329px;
        width: fit-content;
        object-fit: cover;
    }    
}

/*  `xl` applies to large devices (desktops, less than 1200px) */
@media (max-width: 1199.98px) {  }

/* `xxl` applies to x-large devices (large desktops, less than 1400px) */
@media (max-width: 1399.98px) {  }
