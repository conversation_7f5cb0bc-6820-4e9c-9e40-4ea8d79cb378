const HISSE_TUTARI = 4000; // Hisse ba<PERSON><PERSON>na tutar
let secilenHisseSayisi = 0;

// Yardımcı fonksiyonlar
function formatTelefon(input) {
    let telefon = input.value.replace(/\D/g, '');
    telefon = telefon.substring(0, 10);
    
    const match = telefon.match(/^(\d{0,3})(\d{0,3})(\d{0,4})$/);
    if (match) {
        let formatlanan = '';
        if (match[1]) { formatlanan += match[1]; }
        if (match[2]) { formatlanan += match[2].length ? '-' + match[2] : ''; }
        if (match[3]) { formatlanan += match[3].length ? '-' + match[3] : ''; }
        input.value = formatlanan;
    }
}

function telefonKontrol(telefon) {
    return /^\d{3}-\d{3}-\d{4}$/.test(telefon);
}

function showError(input, message) {
    const formGroup = $(input).closest('.form-group');
    $(input).addClass('input-error').removeClass('input-success');
    formGroup.find('.error-message').text(message).show();
}

function showSuccess(input) {
    const formGroup = $(input).closest('.form-group');
    $(input).removeClass('input-error').addClass('input-success');
    formGroup.find('.error-message').hide();
}

function validateInput(input, validationFunction) {
    const value = $(input).val().trim();
    const fieldName = $(input).attr('placeholder') || 'Bu alan';
    
    if (!value) {
        showError(input, `${fieldName} zorunludur`);
        return false;
    }
    
    if (validationFunction && !validationFunction(value)) {
        showError(input, `Geçerli bir ${fieldName.toLowerCase()} giriniz`);
        return false;
    }
    
    showSuccess(input);
    return true;
}

function updateSteps(currentStep) {
    $('.step-item').removeClass('active completed');

    // Tamamlanan step'leri işaretle
    for(let i = 1; i < currentStep; i++) {
        $(`.step-item[data-step="${i}"]`).addClass('completed');
    }

    // Aktif step'i işaretle
    $(`.step-item[data-step="${currentStep}"]`).addClass('active');
}

// Document Ready
$(document).ready(function() {
    // Başvuran bilgileri alanlarına Türkçe karakter kontrolü ekle
    turkceKarakterKontrolu();

    // Input validasyonları
    $(document).on('input', 'input[type="text"], input[type="tel"]', function() {
        if ($(this).attr('type') === 'tel') {
            formatTelefon(this);
            validateInput(this, telefonKontrol);
        } else {
            validateInput(this);
        }
    });

   // Hisse butonları tıklama fonksiyonunda
$('.hisse-button').click(function() {
    $('.hisse-button').removeClass('btn-primary').addClass('btn-outline-primary');
    $(this).removeClass('btn-outline-primary').addClass('btn-primary');
    secilenHisseSayisi = parseInt($(this).data('hisse'));
    
    // Toplam tutar güncellemesi ve animasyon
    const toplamTutarElement = $('#toplamTutar');
    toplamTutarElement.text(secilenHisseSayisi * HISSE_TUTARI);
    
    // Animasyon sınıfını ekle ve kaldır
    toplamTutarElement.parent().addClass('amount-update');
    setTimeout(() => {
        toplamTutarElement.parent().removeClass('amount-update');
    }, 300);
});



    // Adım 1'den 2'ye

$('#step1Next').click(function() {
    if(secilenHisseSayisi === 0) {
        alert('Lütfen hisse sayısı seçiniz');
        return;
    }

    $('#step1').removeClass('active');
    $('#step2').addClass('active');
    $('.progress-bar').css('width', '60%');
    updateSteps(3);
    
    // Hisse sahipleri form alanlarını oluştur
    let html = '';
    for(let i = 1; i <= secilenHisseSayisi; i++) {
        html += `
            <div class="hisse-form mb-4">
                <h5>Hisse Sahibi ${i}</h5>
                <div class="row g-3">
                    <div class="col-md-4">
                        <div class="form-group">
                            <input type="text" class="form-control turkce-karakter-input" name="ad[]" placeholder="Ad" required
                                pattern="[a-zA-ZğüşıöçĞÜŞİÖÇ\\s]{3,}" minlength="3" title="Ad en az 3 karakter olmalı ve sadece Türkçe harfler kullanılabilir">
                            <div class="error-message"></div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <input type="text" class="form-control turkce-karakter-input" name="soyad[]" placeholder="Soyad" required
                                pattern="[a-zA-ZğüşıöçĞÜŞİÖÇ\\s]{2,}" minlength="2" title="Soyad en az 2 karakter olmalı ve sadece Türkçe harfler kullanılabilir">
                            <div class="error-message"></div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <input type="tel" class="form-control" name="telefon[]" 
                                   placeholder="Telefon" maxlength="12" required>
                            <div class="error-message"></div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    $('#hisseSahipleri').html(html);

    // Türkçe karakter kontrolü ekle
    turkceKarakterKontrolu();

    if(secilenHisseSayisi > 1) {
        $('#samePersonDiv').show();
    } else {
        $('#samePersonDiv').hide();
    }
});

    // Aynı kişi checkbox kontrolü
    $('#samePerson').change(function() {
        if(this.checked) {
            $('.hisse-form:not(:first)').hide();
        } else {
            $('.hisse-form').show();
        }
    });

    // Geri butonları
    $('#step2Prev').click(function() {
        $('#step2').removeClass('active');
        $('#step1').addClass('active');
        $('.progress-bar').css('width', '40%');
        updateSteps(2);
    });

    $('#step3Prev').click(function() {
        $('#step3').removeClass('active');
        $('#step2').addClass('active');
        $('.progress-bar').css('width', '60%');
        updateSteps(3);
    });

    $('#step4Prev').click(function() {
        $('#step4').removeClass('active');
        $('#step3').addClass('active');
        $('.progress-bar').css('width', '80%');
        updateSteps(4);
    });

    // İleri butonları
    $('#step2Next').click(function() {
        let isValid = validateStep2();
        if(!isValid) return;

        // Hisse sahiplerini kontrol et
        kontrolHisseSahipleri(function() {
            $('#step2').removeClass('active');
            $('#step3').addClass('active');
            $('.progress-bar').css('width', '80%');
            updateSteps(4);
        });
    });

    $('#step3Next').click(function() {
        let isValid = validateStep3();
        if(!isValid) return;

        $('#odenecekTutar').text(secilenHisseSayisi * HISSE_TUTARI);
        $('#step3').removeClass('active');
        $('#step4').addClass('active');
        $('.progress-bar').css('width', '100%');
        updateSteps(5);
    });

    // Ödeme işlemleri
    $('#kartOdeme').click(function() {
        if(!validateStep4()) return;
        kaydetVeOdemeYap('KART');
    });

    $('#havaleOdeme').click(function() {
        if(!validateStep4()) return;
        $('#havaleModal').modal('show');
    });

    $('#havaleTamamBtn').click(function() {
        kaydetVeOdemeYap('EFT');
    });

    // Kurban Türü adımından sonraki adıma geçiş
    $('#step0Next').click(function() {
        const selected = $('input[name="kurban_turu"]:checked').val();
        if (!selected) {
            $('#kurbanTuruError').text('Lütfen bir kurban türü seçiniz.').show();
            return;
        }
        $('#kurbanTuruError').hide();
        $('#step0').removeClass('active');
        $('#step1').addClass('active');
        $('.progress-bar').css('width', '40%');
        updateSteps(2);
    });

    // Hisse seçimi adımında geri butonu eklemek isterseniz:
    $('#step1Prev').click(function() {
        $('#step1').removeClass('active');
        $('#step0').addClass('active');
        $('.progress-bar').css('width', '20%');
        updateSteps(1);
    });

    $('#whatsappNo').on('click', function() {
        const numara = $(this).text().replace(/\s/g, '');
        // Kopyalama işlemi
        if (navigator.clipboard) {
            navigator.clipboard.writeText(numara).then(function() {
                $('#copySuccess').fadeIn(200);
                setTimeout(function() { $('#copySuccess').fadeOut(400); }, 1200);
            });
        } else {
            // Eski tarayıcılar için
            const tempInput = $('<input>');
            $('body').append(tempInput);
            tempInput.val(numara).select();
            document.execCommand('copy');
            tempInput.remove();
            $('#copySuccess').fadeIn(200);
            setTimeout(function() { $('#copySuccess').fadeOut(400); }, 1200);
        }
    });

    // Sözleşme kutusuna tıklanınca parent'a alert-success ekle
    $('#sozlesmeKabul').on('change', function() {
        var parent = $(this).closest('.card-body');
        if($(this).is(':checked')) {
            parent.addClass('alert-success');
            parent.css('background-color', '#CFFAD5'); // Hafif yeşil
        } else {
            parent.removeClass('alert-success');
            parent.css('background-color', '');
        }
    });
});

// Validasyon fonksiyonları
function validateStep2() {
    let isValid = true;

    if($('#samePerson').is(':checked')) {
        const firstForm = $('.hisse-form').first();
        const adInput = firstForm.find('input[name="ad[]"]');
        const soyadInput = firstForm.find('input[name="soyad[]"]');
        const telefonInput = firstForm.find('input[name="telefon[]"]');

        isValid = validateInput(adInput) && isValid;
        isValid = validateMinLength(adInput) && isValid;
        isValid = validateInput(soyadInput) && isValid;
        isValid = validateMinLength(soyadInput) && isValid;
        isValid = validateInput(telefonInput, telefonKontrol) && isValid;
    } else {
        $('.hisse-form').each(function() {
            const adInput = $(this).find('input[name="ad[]"]');
            const soyadInput = $(this).find('input[name="soyad[]"]');
            const telefonInput = $(this).find('input[name="telefon[]"]');

            isValid = validateInput(adInput) && isValid;
            isValid = validateMinLength(adInput) && isValid;
            isValid = validateInput(soyadInput) && isValid;
            isValid = validateMinLength(soyadInput) && isValid;
            isValid = validateInput(telefonInput, telefonKontrol) && isValid;
        });
    }

    return isValid;
}

function validateStep3() {
    let isValid = true;

    isValid = validateInput($('#basvuranAd')) && isValid;
    isValid = validateInput($('#basvuranSoyad')) && isValid;
    isValid = validateInput($('#basvuranTelefon'), telefonKontrol) && isValid;

    return isValid;
}

function validateStep4() {
    if(!$('#sozlesmeKabul').is(':checked')) {
        alert('Lütfen Vekaletinizi Veriniz.');
        return false;
    }
    return true;
}

function kopyalaIBAN(btn) {
    const iban = $(btn).prev('input').val();
    navigator.clipboard.writeText(iban);
    $(btn).text('Kopyalandı!');
    setTimeout(() => $(btn).text('Kopyala'), 2000);
}

// Türkçe karakter kontrolü fonksiyonu
function turkceKarakterKontrolu() {
    // Tüm Türkçe karakter input alanlarını seç
    $('.turkce-karakter-input').on('input', function() {
        const input = $(this);
        const value = input.val();

        // Geçersiz karakterleri filtrele
        const filteredValue = value.replace(/[^a-zA-ZğüşıöçĞÜŞİÖÇ\s]/g, '');

        // Eğer değer değiştiyse, input'u güncelle
        if (value !== filteredValue) {
            input.val(filteredValue);

            // Uyarı mesajı göster
            showInputWarning(input, 'Sadece Türkçe harfler kullanılabilir');
        }

        // Minimum karakter kontrolü
        validateMinLength(input);
    });

    // Paste olayını da kontrol et
    $('.turkce-karakter-input').on('paste', function(e) {
        const input = $(this);

        setTimeout(function() {
            const value = input.val();
            const filteredValue = value.replace(/[^a-zA-ZğüşıöçĞÜŞİÖÇ\s]/g, '');

            if (value !== filteredValue) {
                input.val(filteredValue);
                showInputWarning(input, 'Yapıştırılan metinde geçersiz karakterler temizlendi');
            }

            // Minimum karakter kontrolü
            validateMinLength(input);
        }, 10);
    });
}

// Minimum karakter uzunluğu kontrolü
function validateMinLength(input) {
    const value = input.val().trim();
    const placeholder = input.attr('placeholder');
    let minLength = 0;
    let fieldName = '';

    // Ad alanları için minimum 3 karakter
    if (placeholder === 'Ad' || input.attr('name') === 'ad' || input.attr('id') === 'basvuranAd') {
        minLength = 3;
        fieldName = 'Ad';
    }
    // Soyad alanları için minimum 2 karakter
    else if (placeholder === 'Soyad' || input.attr('name') === 'soyad' || input.attr('id') === 'basvuranSoyad') {
        minLength = 2;
        fieldName = 'Soyad';
    }

    if (minLength > 0 && value.length > 0 && value.length < minLength) {
        showInputWarning(input, `${fieldName} en az ${minLength} karakter olmalıdır`);
        return false;
    } else if (value.length >= minLength) {
        // Minimum karakter şartı sağlandıysa uyarıyı kaldır
        input.removeClass('is-invalid');
        input.next('.invalid-feedback').remove();
        return true;
    }

    return true;
}

// Input uyarı mesajı gösterme fonksiyonu
function showInputWarning(input, message) {
    // Mevcut uyarıyı temizle
    input.removeClass('is-invalid');
    input.next('.invalid-feedback').remove();

    // Yeni uyarı ekle
    input.addClass('is-invalid');
    input.after(`<div class="invalid-feedback">${message}</div>`);

    // 3 saniye sonra uyarıyı kaldır
    setTimeout(function() {
        input.removeClass('is-invalid');
        input.next('.invalid-feedback').remove();
    }, 3000);
}

// Hisse sahiplerini kontrol eden fonksiyon
function kontrolHisseSahipleri(callback) {
    console.log('kontrolHisseSahipleri fonksiyonu çalışıyor...');
    const hisseSahipleri = [];

    if($('#samePerson').is(':checked')) {
        // Aynı kişi seçiliyse, sadece ilk formdaki bilgileri al
        const firstForm = $('.hisse-form').first();
        const hisseSahibi = {
            ad: firstForm.find('input[name="ad[]"]').val().trim(),
            soyad: firstForm.find('input[name="soyad[]"]').val().trim()
        };

        // Seçilen hisse sayısı kadar aynı kişiyi ekle
        for(let i = 0; i < secilenHisseSayisi; i++) {
            hisseSahipleri.push(hisseSahibi);
        }
    } else {
        // Her form alanından bilgileri al
        $('.hisse-form').each(function() {
            const ad = $(this).find('input[name="ad[]"]').val().trim();
            const soyad = $(this).find('input[name="soyad[]"]').val().trim();

            if(ad && soyad) {
                hisseSahipleri.push({ ad: ad, soyad: soyad });
            }
        });
    }

    console.log('Kontrol edilecek hisse sahipleri:', hisseSahipleri);

    // AJAX ile kontrol et
    $.ajax({
        url: '/islemler/HisseSahipleriKontrol.php',
        method: 'POST',
        data: JSON.stringify({ hisse_sahipleri: hisseSahipleri }),
        contentType: 'application/json',
        dataType: 'json',
        success: function(response) {
            console.log('AJAX başarılı, yanıt:', response);
            if(response.success && response.bulunan_kayitlar.length > 0) {
                console.log('Bulunan kayıtlar var, uyarı gösteriliyor...');
                // Bulunan kayıtlar varsa uyarı göster
                gosterUyariMesaji(response.bulunan_kayitlar, callback);
            } else {
                console.log('Bulunan kayıt yok, devam ediliyor...');
                // Bulunan kayıt yoksa direkt devam et
                callback();
            }
        },
        error: function(xhr, status, error) {
            console.error('Hisse sahipleri kontrol hatası:', error);
            console.error('XHR:', xhr);
            // Hata durumunda da devam et
            callback();
        }
    });
}

// SweetAlert ile uyarı mesajı göster
function gosterUyariMesaji(bulunanKayitlar, callback) {
    // SweetAlert varsa kullan, yoksa normal confirm kullan
    if (typeof Swal !== 'undefined') {
        // HTML formatında güzel bir mesaj oluştur
        let htmlMesaj = `
            <div style="text-align: left; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">
                <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 3px; margin-bottom: 3px;">
                    <div style="display: flex; align-items: center; margin-bottom: 2px;">
                        <i class="bx bx-error" style="color: #f39c12; font-size: 20px; margin-right: 5px;"></i>
                        <strong style="color: #856404; font-size: 16px;">Aşağıdaki hissedarlar daha önce kayıt edilmiş:</strong>
                    </div>
                    <div style="max-height: 200px; overflow-y: auto;">
        `;

        bulunanKayitlar.forEach(function(kayit) {
            htmlMesaj += `
                <div style="background: white; border-left: 4px solid #f39c12; padding: 10px; margin: 8px 0; border-radius: 4px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                    <div style="display: flex; align-items: center; justify-content: space-between;">
                        <div>
                            <i class="bx bx-user" style="color: #3498db; margin-right: 8px; font-size: 16px;"></i>
                            <strong style="color: #2c3e50; font-size: 15px;">${kayit.tam_isim}</strong>
                        </div>
                        <div style="background: #e74c3c; color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: bold;">
                            <i class="bx bx-calendar" style="margin-right: 4px;"></i>${kayit.tarih}
                        </div>
                    </div>
                </div>
            `;
        });

        htmlMesaj += `
                    </div>
                </div>
                <div style="background: #e8f4fd; border: 1px solid #bee5eb; border-radius: 8px; padding: 15px; text-align: center;">
                    <p style="margin: 0; color: #0c5460; font-size: 15px; font-weight: 500;">
                        <i class="bx bx-help-circle" style="color: #17a2b8; margin-right: 8px; font-size: 18px;"></i>
                        <strong>Belki isim benzerliğidir.</strong><br>
                        <span style="font-size: 14px;">Yine de kayıt yapmak istiyor musunuz?</span>
                    </p>
                </div>
            </div>
        `;

        Swal.fire({
            title: '<span style="color: #e74c3c;"><i class="bx bx-error-circle"></i> Dikkat!</span>',
            html: htmlMesaj,
            icon: null, // HTML'de kendi ikonumuzu kullanıyoruz
            showCancelButton: true,
            confirmButtonColor: '#28a745',
            cancelButtonColor: '#dc3545',
            confirmButtonText: '<i class="bx bx-check"></i> Evet, Kayıt Yap',
            cancelButtonText: '<i class="bx bx-x"></i> Hayır, İptal Et',
            customClass: {
                popup: 'swal-wide',
                title: 'swal-title-custom',
                confirmButton: 'swal-confirm-custom',
                cancelButton: 'swal-cancel-custom'
            },
            buttonsStyling: true,
            allowOutsideClick: false,
            allowEscapeKey: false,
            focusConfirm: false,
            focusCancel: true
        }).then((result) => {
            if (result.isConfirmed) {
                // Kullanıcı "Evet" dedi, devam et
                callback();
            }
            // Kullanıcı "Hayır" dedi veya kapatırsa hiçbir şey yapma
        });
    } else {
        // SweetAlert yüklenmemişse normal confirm kullan
        console.warn('SweetAlert2 yüklenmemiş, normal confirm kullanılıyor');
        let mesaj = 'Aşağıdaki hissedarlar daha önce kayıt edilmiş:\n\n';

        bulunanKayitlar.forEach(function(kayit) {
            mesaj += `• ${kayit.tam_isim} - ${kayit.tarih} tarihinde kayıt edilmiş\n`;
        });

        mesaj += '\nBelki isim benzerliğidir. Yine de kayıt yapalım mı?';

        const result = confirm(mesaj);
        if (result) {
            callback();
        }
    }
}

function kaydetVeOdemeYap(odemeTipi) {
    const formData = {
        hisse_sayisi: secilenHisseSayisi,
        hisse_tutari: HISSE_TUTARI,
        odeme_tipi: odemeTipi,
        kurban_turu: $('input[name="kurban_turu"]:checked').val(),
        basvuran: {
            ad: $('#basvuranAd').val(),
            soyad: $('#basvuranSoyad').val(),
            telefon: $('#basvuranTelefon').val()
        },
        hisse_sahipleri: []
    };

    if($('#samePerson').is(':checked')) {
        const hisseSahibi = {
            ad: $('.hisse-form').first().find('input[name="ad[]"]').val(),
            soyad: $('.hisse-form').first().find('input[name="soyad[]"]').val(),
            telefon: $('.hisse-form').first().find('input[name="telefon[]"]').val()
        };
        for(let i = 0; i < secilenHisseSayisi; i++) {
            formData.hisse_sahipleri.push(hisseSahibi);
        }
    } else {
        $('.hisse-form').each(function() {
            formData.hisse_sahipleri.push({
                ad: $(this).find('input[name="ad[]"]').val(),
                soyad: $(this).find('input[name="soyad[]"]').val(),
                telefon: $(this).find('input[name="telefon[]"]').val()
            });
        });
    }

    $.ajax({
        url: '/islemler/KurbanKayitFormu-Kaydet.php',
        method: 'POST',
        data: JSON.stringify(formData),
        contentType: 'application/json',
        dataType: 'json',
        success: function(response) {
            console.log('AJAX Başarılı Yanıt:', response);

            if (response.success === true) {
                $('#havaleModal').modal('hide');
                $('#basariliModal').modal('show');
            } else {
                let errorMsg = 'Bir hata oluştu.';
                if (response.error) errorMsg = response.error;
                else if (response.mesaj) errorMsg = response.mesaj;
                
                console.error('Sunucu Hatası:', errorMsg);
                alert('Hata: ' + errorMsg);
            }
        },
        error: function(xhr, status, error) {
            console.error('AJAX Hatası:', {
                status: status,
                error: error,
                responseText: xhr.responseText
            });
            
            let errorMessage = 'Bir hata oluştu. Lütfen tekrar deneyin.';
            try {
                const response = JSON.parse(xhr.responseText);
                if (response.error) {
                    errorMessage = response.error;
                }
                if (response.error) errorMsg = response.error;
                else if (response.mesaj) errorMsg = response.mesaj;
            } catch (e) {
                console.error('Yanıt ayrıştırma hatası:', e);
            }
            
            alert('Hata: ' + errorMsg + '\n\nDetaylar için konsolu kontrol edin (F12).');
        }
    });


  /*  $.ajax({
        url: '/islemler/KurbanKayitFormu-Kaydet.php',
        method: 'POST',
        data: JSON.stringify(formData),
        contentType: 'application/json',
        success: function(response) {
            $('#havaleModal').modal('hide');
            $('#basariliModal').modal('show');
        },
        error: function() {
            alert('Bir hata oluştu. Lütfen tekrar deneyiniz.');
        }
    }); */
}