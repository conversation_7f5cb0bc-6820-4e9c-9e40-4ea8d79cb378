// Üst Menü i<PERSON><PERSON> Kaydırma İşleminde Yapılacaklar

//Html DOM içerikleri yüklendiğinde bunu calıstır.
window.addEventListener("DOMContentLoaded", event => {

    //MenuUsteSabitle isminde bir fonksiyon tanımlıyoruz.
    var  MenuUsteSabitle=function(){
        const CssSinifEkle=document.body.querySelector("#ustmenualan"); //navbar'ın ID'sini CssSinifEkle adında bir degiskene tanimliyoruz. 
        if(!CssSinifEkle){ 
            return; //eger CssSinifEkle degiskeni bos ise fonksiyonu sonlandırıyoruz.
        }
        if(window.scrollY===0){ // Eğer ekranın y ekseni 0 ise, yani aşağıya doğru bir kaydırma yoksa 'ust-sabitle' css sınıfını kaldırıyoruz.
            CssSinifEkle.classList.remove("ust-sabitle");
        }else{ // Eger ekranın y ekseni 0'dan farklı ise, yani aşağıya kaydırılmıs ise 'ust-sabitle' css sınıfını ekliyoruz.
            CssSinifEkle.classList.add("ust-sabitle");
        }
    };
    MenuUsteSabitle(); //tanımladıgımız fonksiyonu calışır hale getiriyoruz.
    document.addEventListener("scroll",MenuUsteSabitle); //sayfada gerçekleşecek bir olay dinleme islemi yapıyoruz. Eğer scroll olayı gerçekleşirse 'MenuUsteSabitle' fonksiyonunu calıstırıyoruz.
   
});

//Canvas Menü Elelamanlarına Tıklandıgında Yapılacaklar Canvas'ın kapanmasını istiyoruz.

var canvaskapat = document.querySelectorAll(".canvas-kapat"); // .canvas-kapat sınıfındaki elemanları canvaskapat adında bir diziye atıyoruz.
for (let say = 0; say < canvaskapat.length; say++) { // dizideki elaman sayısı kadar döngü olusturuyoruz.
    canvaskapat[say].addEventListener("click", function () { //her tıklanılan elemanın click olayını dinliyoruz.
        document.querySelector('[data-bs-dismiss="offcanvas"]').click(); //offcanvas'ın kapanmasını tetikliyoruz.
    })
   
}

