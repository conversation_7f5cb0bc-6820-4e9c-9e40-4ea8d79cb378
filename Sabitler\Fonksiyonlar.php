<?php
function temizle($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

function telefonFormati($telefon) {
    // Sadece rakamları al
    $telefon = preg_replace('/[^0-9]/', '', $telefon);
    
    // Türkiye formatına çevir
    if(strlen($telefon) == 10) {
        return "+90" . $telefon;
    } else if(strlen($telefon) == 11 && substr($telefon, 0, 1) == "0") {
        return "+9" . $telefon;
    }
    return $telefon;
}

function hataYaz($mesaj) {
    return json_encode(['error' => $mesaj]);
}

function basariYaz($data = null) {
    $response = [
        'success' => true,
        'mesaj' => 'Kayıt başarıyla tamamlandı.'
    ];

    echo json_encode($response);
    return json_encode($response);
}

?>