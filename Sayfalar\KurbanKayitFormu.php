<?php
include_once ANA_DIZIN . '/Sabitler/Fonksiyonlar.php';

$KartlaOdemeDurum = "Durgun"; // veya "Faal" yaparsanız buton görünür olur
?>

    <div class="container-fluid mt-3">
        <div class="steps p-1">
            <div class="step-item active" data-step="1">
                <div class="step-circle">1</div>
                <div class="step-title">Kurban Türü</div>
            </div>
            <div class="step-item" data-step="2">
                <div class="step-circle">2</div>
                <div class="step-title">Hisse <PERSON>çimi</div>
            </div>
            <div class="step-item" data-step="3">
                <div class="step-circle">3</div>
                <div class="step-title">Hisse Sahipleri</div>
            </div>
            <div class="step-item" data-step="4">
                <div class="step-circle">4</div>
                <div class="step-title">Başvuran Bilgileri</div>
            </div>
            <div class="step-item" data-step="5">
                <div class="step-circle">5</div>
                <div class="step-title">Ödeme</div>
            </div>
        </div>
        <div class="progress">
            <div class="progress-bar" role="progressbar" style="width: 20%"></div>
        </div>

        <!-- Adım 1: Kurban Türü -->
        <div class="step p-2 active" id="step0">
            <h3 class="fs-6 fs-md-2 text-center p-3">Bağışlanacak Kurban Türü</h3>
            <div class="row ">
                <div class="col-12">
                    <div class="container kurban-turu-group justify-content-center">
                        <div class="col-12 col-md-4 col-lg-2 m-md-1">
                            <label class="kurban-turu-card w-100 position-relative">
                                <input type="radio" name="kurban_turu" value="Vacip" required class="kurban-radio">
                                <img src="/Depolar/DepoResim/inek-1.png" class="w-50"></img>
                                <span class="KurbanTurBaslik" >Vacip Kurban<br><small>(Kurban Bayramı Boyunca Kesilen)</small></span>
                            </label>
                        </div>
                        <div class="col-6 col-md-4 col-lg-2 m-md-1">
                            <label class="kurban-turu-card w-100 position-relative">
                                <input type="radio" name="kurban_turu" value="Adak" class="kurban-radio">
                                <img src="/Depolar/DepoResim/inek-1.png" class="w-50"></img> 
                                <span class="KurbanTurBaslik" >Adak Kurbanı</span>
                            </label>
                        </div>
                        <div class="col-6 col-md-4 col-lg-2 m-md-1">
                            <label class="kurban-turu-card w-100 position-relative">
                                <input type="radio" name="kurban_turu" value="Akika" class="kurban-radio">
                                <img src="/Depolar/DepoResim/inek-1.png" class="w-50"></img>
                                <span class="KurbanTurBaslik" >Akika Kurbanı</span>
                            </label>
                        </div>
                        <div class="col-6 col-md-4 col-lg-2 m-md-1">
                            <label class="kurban-turu-card w-100 position-relative">
                                <input type="radio" name="kurban_turu" value="Şükür" class="kurban-radio">
                                <img src="/Depolar/DepoResim/inek-1.png" class="w-50"></img>
                                <span class="KurbanTurBaslik" >Şükür Kurbanı</span>
                            </label>
                        </div>
                        <div class="col-6 col-md-4 col-lg-2 m-md-1">
                            <label class="kurban-turu-card w-100 position-relative">
                                <input type="radio" name="kurban_turu" value="Nafile" class="kurban-radio">
                                <img src="/Depolar/DepoResim/inek-1.png" class="w-50"></img>
                                <span class="KurbanTurBaslik" >Nafile Kurbanı</span>
                            </label>
                        </div>
                    </div>

                    <div class="error-message mt-2" id="kurbanTuruError" style="display:none;"></div>
                </div>
            </div>
            <div class="row mt-2">
                <div class="col text-center">
                    <button class="btn btn-primary col-12 col-md-6" id="step0Next">Sonraki</button>
                </div>
            </div>
        </div>

        <!-- Adım 2: Hisse Seçimi -->
        <div class="step p-2" id="step1">
            <h3 class="fs-6 fs-md-2 text-center p-3">Hisse Sayısı Seçimi</h3>
            <div class="row mb-4">
                <div class="col text-center">
                    <?php for($i=1; $i<=7; $i++) { ?>
                    <button class="btn btn-outline-primary hisse-button col-4 "
                        data-hisse="<?php echo $i; ?>"><?php echo $i; ?> Hisse</button>
                    <?php } ?>
                </div>
            </div>
            <div class="row mb-4">
                <div class="col-lg-6 m-auto">
                    <div class="total-amount-card">
                        <div class="amount-label">Toplam Tutar</div>
                        <div class="amount-value">
                            <span id="toplamTutar">0</span>
                            <span class="currency">TL</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row row-cols-2">
                <div class="col text-start">
                    <button class="btn btn-secondary w-100" id="step1Prev">Geri</button>
                </div>
                <div class="col text-end">
                    <button class="btn btn-primary w-100" id="step1Next">Sonraki</button>
                </div>
            </div>
        </div>

        <!-- Adım 3: Hisse Sahipleri -->
        <div class="step p-2" id="step2">
            <h3 class="fs-6 fs-md-2 text-center p-3">Hisse Sahiplerinin Bilgileri</h3>
            <div class="form-check mb-3" id="samePersonDiv" style="display:none;">
                <input class="form-check-input" type="checkbox" id="samePerson">
                <label class="form-check-label" for="samePerson">
                    Bütün hisseler aynı isimde olacak
                </label>
            </div>

            <div id="hisseSahipleri"></div>

            <div class="row mt-4 row-cols-2">
                <div class="col">
                    <button class="btn btn-secondary w-100" id="step2Prev">Geri</button>
                </div>
                <div class="col text-end">
                    <button class="btn btn-primary w-100" id="step2Next">İleri</button>
                </div>
            </div>
        </div>

        <!-- Adım 4: Başvuran Bilgileri -->
        <div class="step p-2" id="step3">
            <h3 class="fs-6 fs-md-2 text-center p-3">Başvuran Bilgileri</h3>

            <div class="row g-3 mb-4">
                <div class="col-md-4">
                    <div class="form-group">
                        <label class="form-label">Adınız</label>
                        <input type="text" class="form-control" id="basvuranAd" name="ad" placeholder="Ad" required
                            pattern="[a-zA-ZğüşıöçĞÜŞİÖÇ\s]+" title="Sadece harfler kullanılabilir">
                        <div class="error-message"></div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label class="form-label">Soyadınız</label>
                        <input type="text" class="form-control" id="basvuranSoyad" name="soyad" placeholder="Soyad"
                            required pattern="[a-zA-ZğüşıöçĞÜŞİÖÇ\s]+" title="Sadece harfler kullanılabilir">
                        <div class="error-message"></div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label class="form-label">Telefon Numaranız</label>
                        <input type="tel" class="form-control" id="basvuranTelefon" placeholder="Telefon" maxlength="12"
                            required>
                        <div class="error-message"></div>
                    </div>
                </div>
            </div>
            <div class="row mt-4 row-cols-2">
                <div class="col">
                    <button class="btn btn-secondary w-100" id="step3Prev">Geri</button>
                </div>
                <div class="col text-end">
                    <button class="btn btn-primary w-100" id="step3Next">İleri</button>
                </div>
            </div>
        </div>

        <!-- Adım 5: Sözleşme ve Ödeme -->
        <div class="step p-2" id="step4">
            <h3 class="fs-6 fs-md-2 text-center p-3">Vekaletname & Onay </h3>
            <div class="card mb-4">
                <div class="card-body text-center">
                    <p class="card-text">
                    <div class="alert alert-danger">
                        <strong>ÖNEMLİ NOT:</strong> <br>
                        Kurban bağışının gerçekleşmesi için ödeme dekontunu whatsapp numarasına göndermeniz gerekiyor.
                        <br>

                        <button id="whatsappNo" type="button"
                            class="btn btn-outline-success d-inline-flex align-items-center mt-3"
                            style="user-select:all;" title="Kopyalamak için tıklayın">
                            <i class="bx bxl-whatsapp me-2" style="font-size:1.3em;"></i>
                            0536 848 50 98
                        </button>
                        <span id="copySuccess"
                            style="display:none; color:green; font-size:0.95em; margin-left:8px;">Kopyalandı!</span>
                    </div>
                    </p>
                    <div class="form-check mb-3 text-start" id="onayalani">
                        <input class="form-check-input" type="checkbox" id="sozlesmeKabul" required>
                        <label class="form-check-label" for="sozlesmeKabul">
                            Kurbanımı/Kurbanlarımın vekaletini verdim.
                        </label>
                    </div>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-body alert alert-success">
                    <h4 class="card-title">Ödenecek Toplam Tutar: <span id="odenecekTutar">0</span> TL</h4>
                </div>
            </div>

            <div class="row mt-4 row-cols-auto">
                <div class="col-4">
                    <button class="btn btn-secondary w-100 " id="step4Prev">Geri</button>
                </div>
                <?php if($KartlaOdemeDurum == "Faal"): ?>
                <div class="col-4">
                    <button class="btn btn-success w-100 " id="kartOdeme">Banka Kartıyla Ödeme</button>
                </div>
                <?php endif; ?>
                <div class="col-8">
                    <button class="btn btn-primary w-100 " id="havaleOdeme">EFT & Havale Bilgileri</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Havale Bilgileri Modal -->
    <div class="modal fade" id="havaleModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Banka Hesap Bilgileri</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="fw-bold">KUVEYT TÜRK BANKASI</label>
                        <div class="input-group">
                            <input type="text" class="form-control" value="TR21 0020 5000 0061 5018 1000 01" readonly>
                            <button class="btn btn-outline-primary" onclick="kopyalaIBAN(this)">Kopyala</button>
                        </div>

                    </div>
                    <div class="mb-3"> <label class="fw-bold">ZİRAAT BANKASI</label>
                        <div class="input-group">
                            <input type="text" class="form-control" value="TR86 0001 0017 2945 3996 1150 02" readonly>
                            <button class="btn btn-outline-primary" onclick="kopyalaIBAN(this)">Kopyala</button>
                        </div>
                    </div>
                    <div class="alert alert-info">
                        Lütfen açıklama kısmına Başvuran Ad ve Soyadını yazmayı unutmayınız.! <br>
                        Dekontu göndermeyi unutmayın.! <br>
                        <span class="text-danger fw-bold"> Dekont gönderilmediği takdirde Kurbanınız kesilmeyebilir ve
                            bu sorumluluğu kabul etmiyoruz.</span>
                    </div>

                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" id="havaleTamamBtn">Tamam, Kaydet</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Başarılı Modal -->
    <div class="modal fade" id="basariliModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <!-- <div class="modal-header">
                    <h5 class="modal-title">İşlem Başarılı</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div> -->
                <div class="modal-body">
                    <div class="alert alert-success">
                        <h5 class="text-center">Başvurunuz Kayıt edilmiştir. </h5> <br>
                        <div class="alert alert-success text-center">
                            <a href="https://wa.me/905368485098?text=Kurban%20Bağışı%20için%20başvuru%20formunu%20doldurdum."
                                target="_blank" class="fw-bold btn btn-light align-items-center w-100"
                                style="text-decoration:none; font-size:1.15rem; display:inline-flex; align-items:center; gap:8px;"
                                onclick="window.location.reload()">
                                
                                <div class="text-center btn btn-outline-success d-flex flex-column align-items-center">
                                <i class="bx bxl-whatsapp" style="font-size:4rem;"></i>
                                <span class="fs-5 fs-md-4"> Kaydınızı WhatsApp'tan Göndermek için  </span> 
                            <div class="row d-flex align-items-center justify-content-center gap-2 mt-1" >
                                <i class="bx  bx-checkbox bx-md text-danger"></i>  
                                <span class="bg-danger text-white px-1 py-1 rounded" style="margin-top: -6px;">Tıklayınız.</span> 
                            </div>   
                            
                            </div>
                                
                            </a>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>

    <?php
      include_once ANA_DIZIN . '/Sabitler/Altbaglantilar.php';
     ?>

    <!-- SweetAlert2 CDN -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script src="<?php echo SITE_URL; ?>/Kutuphane/js/KurbanBagisForm-JS.js"></script>
