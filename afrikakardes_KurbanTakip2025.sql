-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.2
-- https://www.phpmyadmin.net/
--
-- Anamakine: localhost:3306
-- <PERSON><PERSON><PERSON>: 17 May 2025, 00:16:51
-- <PERSON><PERSON><PERSON> sürü<PERSON>ü: 8.0.42
-- <PERSON>HP Sürümü: 7.4.33

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- <PERSON><PERSON><PERSON><PERSON><PERSON>: `afrikakardes_KurbanTakip2025`
--

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `gruplar`
--

CREATE TABLE `gruplar` (
  `grup_id` int NOT NULL,
  `grup_durumu` enum('DOLU','BOS') CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT 'BOS',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `grup_turu` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL DEFAULT 'Normal'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;

--
-- Tablo döküm verisi `gruplar`
--

INSERT INTO `gruplar` (`grup_id`, `grup_durumu`, `created_at`, `grup_turu`) VALUES
(20, 'DOLU', '2025-05-06 20:50:35', 'Vacip'),
(21, 'DOLU', '2025-05-06 20:56:05', 'Diğer'),
(22, 'DOLU', '2025-05-06 21:09:35', 'Diğer'),
(23, 'DOLU', '2025-05-06 21:14:52', 'Diğer'),
(24, 'DOLU', '2025-05-06 21:31:16', 'Diğer'),
(25, 'DOLU', '2025-05-06 21:40:07', 'Diğer'),
(26, 'DOLU', '2025-05-07 20:12:47', 'Diğer'),
(27, 'BOS', '2025-05-07 20:15:32', 'Diğer'),
(28, 'DOLU', '2025-05-08 10:05:31', 'Vacip'),
(29, 'BOS', '2025-05-08 10:15:12', 'Vacip');

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `hisseler`
--

CREATE TABLE `hisseler` (
  `hisse_id` int NOT NULL,
  `grup_id` int DEFAULT NULL,
  `kurban_turu` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `ad` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `soyad` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `telefon` varchar(15) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `basvuran_ad` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `basvuran_soyad` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `basvuran_telefon` varchar(15) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `hisse_tutari` decimal(10,2) DEFAULT NULL,
  `odeme_tipi` enum('KART','EFT') CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `odeme_durumu` enum('BEKLIYOR','ODENDI') CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT 'BEKLIYOR',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;

--
-- Tablo döküm verisi `hisseler`
--

INSERT INTO `hisseler` (`hisse_id`, `grup_id`, `kurban_turu`, `ad`, `soyad`, `telefon`, `basvuran_ad`, `basvuran_soyad`, `basvuran_telefon`, `hisse_tutari`, `odeme_tipi`, `odeme_durumu`, `created_at`) VALUES
(223, 21, 'Şükür', 'Emel', 'Şengül', '+905353084676', 'Emel', 'Şengül', '+905353084676', 4000.00, 'EFT', 'BEKLIYOR', '2025-05-08 09:47:25'),
(247, 21, 'Adak', 'Mehmet Nuri', 'Taş', '+903333333333', 'M.Emin', 'Tayfur', '+905555555555', 4000.00, 'EFT', 'BEKLIYOR', '2025-05-08 10:21:11'),
(248, 21, 'Adak', 'Mehmet Nuri', 'Taş', '+903333333333', 'M.Emin', 'Tayfur', '+905555555555', 4000.00, 'EFT', 'BEKLIYOR', '2025-05-08 10:21:11'),
(249, 21, 'Akika', 'Ali', 'Demir', '+902222222222', 'Şükrü', 'Değirmen', '+905555555555', 4000.00, 'EFT', 'BEKLIYOR', '2025-05-08 10:33:22'),
(250, 21, 'Akika', 'Ali', 'Demir', '+902222222222', 'Şükrü', 'Değirmen', '+905555555555', 4000.00, 'EFT', 'BEKLIYOR', '2025-05-08 10:33:22'),
(251, 22, 'Adak', 'Şükriye', 'Şükür', '+905555555555', 'Şehmus', 'Çağırman', '+905555555555', 4000.00, 'EFT', 'BEKLIYOR', '2025-05-08 10:34:56'),
(252, 22, 'Adak', 'Şükriye', 'Şükür', '+905555555555', 'Şehmus', 'Çağırman', '+905555555555', 4000.00, 'EFT', 'BEKLIYOR', '2025-05-08 10:34:56'),
(253, 22, 'Adak', 'Şükriye', 'Şükür', '+905555555555', 'Şehmus', 'Çağırman', '+905555555555', 4000.00, 'EFT', 'BEKLIYOR', '2025-05-08 10:34:56'),
(254, 20, 'Vacip', 'Şaban', 'Sakar', '+905353084676', 'Emel', 'Şengül', '+905353084676', 4000.00, 'EFT', 'BEKLIYOR', '2025-05-08 19:20:10'),
(255, 20, 'Vacip', 'Q', 'Y', '+901111114444', 'T', 'L', '+903333333333', 4000.00, 'EFT', 'BEKLIYOR', '2025-05-10 02:50:06'),
(256, 20, 'Vacip', 'Gülbeyaz', 'Yürek', '+905520288854', 'Gülbeyaz', 'Yürek', '+905520288854', 4000.00, 'EFT', 'BEKLIYOR', '2025-05-10 12:52:20'),
(257, 20, 'Vacip', 'Gülbeyaz', 'Yürek', '+905520288854', 'Merve', 'Yürek', '+905520288854', 4000.00, 'EFT', 'BEKLIYOR', '2025-05-10 12:55:36'),
(258, 20, 'Vacip', 'Eylül', 'YILMAZ', '+905052632320', 'Eylül', 'YILMAZ', '+905052632320', 4000.00, 'EFT', 'BEKLIYOR', '2025-05-11 06:36:18'),
(259, 22, 'Adak', 'gfgffgfg', 'fgfgfgf', '+903434343434', 'sdfsdfsdf', 'sdfsfdsdf', '+904344444444', 4000.00, 'EFT', 'BEKLIYOR', '2025-05-12 13:33:30'),
(260, 22, 'Adak', 'gfgffgfg', 'fgfgfgf', '+903434343434', 'sdfsdfsdf', 'sdfsfdsdf', '+904344444444', 4000.00, 'EFT', 'BEKLIYOR', '2025-05-12 13:33:30'),
(261, 22, 'Adak', 'gfgffgfg', 'fgfgfgf', '+903434343434', 'sdfsdfsdf', 'sdfsfdsdf', '+904344444444', 4000.00, 'EFT', 'BEKLIYOR', '2025-05-12 13:33:30'),
(262, 20, 'Vacip', 'R', 'H', '+903333333333', 'T', 'K', '+900000000000', 4000.00, 'EFT', 'BEKLIYOR', '2025-05-14 00:20:06'),
(263, 20, 'Vacip', 'EFE', 'YENER', '+905070632748', 'EFE', 'YENER', '+905070632748', 4000.00, 'EFT', 'BEKLIYOR', '2025-05-14 12:46:15'),
(264, 21, 'Adak', 'Mehmet', 'Korkmaz', '+905419304330', 'Mehmet', 'Korkmaz', '+905419304330', 4000.00, 'EFT', 'BEKLIYOR', '2025-05-14 17:16:47'),
(265, 21, 'Akika', 'Mehmet', 'Korkmaz', '+905419304330', 'Mehmet', 'Korkmaz', '+905419304330', 4000.00, 'EFT', 'BEKLIYOR', '2025-05-14 17:18:36'),
(266, 28, 'Vacip', 'Muhittin', 'Turgut', '+905058138239', 'Muhittin', 'Turgut', '+905058138239', 4000.00, 'EFT', 'BEKLIYOR', '2025-05-15 07:04:09'),
(267, 28, 'Vacip', 'Büşra', 'Sezgin', '+905315640022', 'Büşra', 'Sezgin', '+905315640022', 4000.00, 'EFT', 'BEKLIYOR', '2025-05-15 08:01:08'),
(268, 28, 'Vacip', 'PAKİZE', 'PAMUKCU', '+905071165723', 'Özkan', 'Pamukcu', '+905071165723', 4000.00, 'EFT', 'BEKLIYOR', '2025-05-15 08:57:51'),
(269, 28, 'Vacip', 'A', 'B', '+905555555555', 'A', 'B', '+905555555555', 4000.00, 'EFT', 'BEKLIYOR', '2025-05-15 12:21:21'),
(270, 28, 'Vacip', 'İbrahim', 'Eroğlu', '+900506222211', 'İbrahim', 'Eroğlu', '+900506222211', 4000.00, 'EFT', 'BEKLIYOR', '2025-05-15 14:05:05'),
(271, 28, 'Vacip', 'İbrahim', 'Eroğlu', '+900506222211', 'İbrahim', 'Eroğlu', '+900506222211', 4000.00, 'EFT', 'BEKLIYOR', '2025-05-15 14:10:06'),
(272, 28, 'Vacip', 'Nurcan', 'Kaya', '+905054966717', 'Nurcan', 'Kaya', '+905054966717', 4000.00, 'EFT', 'BEKLIYOR', '2025-05-15 15:12:08'),
(273, 29, 'Vacip', 'Murat', 'Mert', '+905077729061', 'Murat', 'Mert', '+905077729061', 4000.00, 'EFT', 'BEKLIYOR', '2025-05-16 11:41:50');

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `YP-Yoneticiler`
--

CREATE TABLE `YP-Yoneticiler` (
  `YoneticiID` int UNSIGNED NOT NULL,
  `YoneticiKA` varchar(50) NOT NULL,
  `YoneticiPR` varchar(255) NOT NULL,
  `YoneticiKT` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `YoneticiSI` varchar(45) DEFAULT NULL,
  `YoneticiAD` varchar(50) NOT NULL,
  `YoneticiSY` varchar(50) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Tablo döküm verisi `YP-Yoneticiler`
--

INSERT INTO `YP-Yoneticiler` (`YoneticiID`, `YoneticiKA`, `YoneticiPR`, `YoneticiKT`, `YoneticiSI`, `YoneticiAD`, `YoneticiSY`) VALUES
(2, 'admin', '$2y$10$oLXq9qjdXwjvKgQ9puqnXuE7.KZ7VkzbNrNuO7Yn6RXZ/LY718V26', '2025-05-08 14:20:21', '0001', 'Admin', 'Kullanıcı');

--
-- Dökümü yapılmış tablolar için indeksler
--

--
-- Tablo için indeksler `gruplar`
--
ALTER TABLE `gruplar`
  ADD PRIMARY KEY (`grup_id`);

--
-- Tablo için indeksler `hisseler`
--
ALTER TABLE `hisseler`
  ADD PRIMARY KEY (`hisse_id`),
  ADD KEY `grup_id` (`grup_id`);

--
-- Tablo için indeksler `YP-Yoneticiler`
--
ALTER TABLE `YP-Yoneticiler`
  ADD PRIMARY KEY (`YoneticiID`);

--
-- Dökümü yapılmış tablolar için AUTO_INCREMENT değeri
--

--
-- Tablo için AUTO_INCREMENT değeri `gruplar`
--
ALTER TABLE `gruplar`
  MODIFY `grup_id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=30;

--
-- Tablo için AUTO_INCREMENT değeri `hisseler`
--
ALTER TABLE `hisseler`
  MODIFY `hisse_id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=274;

--
-- Tablo için AUTO_INCREMENT değeri `YP-Yoneticiler`
--
ALTER TABLE `YP-Yoneticiler`
  MODIFY `YoneticiID` int UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- Dökümü yapılmış tablolar için kısıtlamalar
--

--
-- Tablo kısıtlamaları `hisseler`
--
ALTER TABLE `hisseler`
  ADD CONSTRAINT `hisseler_ibfk_1` FOREIGN KEY (`grup_id`) REFERENCES `gruplar` (`grup_id`);
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
