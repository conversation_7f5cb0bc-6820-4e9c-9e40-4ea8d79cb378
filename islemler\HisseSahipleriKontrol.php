<?php
header('Content-Type: application/json');
include_once $_SERVER['DOCUMENT_ROOT'] . '/Sabitler/GenelAyarlar.php';
include_once ANA_DIZIN . '/Sabitler/VT.php';
include_once ANA_DIZIN . '/Sabitler/Fonksiyonlar.php';

$database = new Database();
$db = $database->getConnection();

if(!$db) {
    http_response_code(500);
    echo json_encode(['success' => false, 'mesaj' => 'Veritabanı bağlantı hatası']);
    exit;
}

$data = json_decode(file_get_contents('php://input'), true);

if(!isset($data['hisse_sahipleri']) || !is_array($data['hisse_sahipleri'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'mesaj' => 'Geçersiz veri']);
    exit;
}

try {
    $bulunan_kayitlar = [];
    
    foreach($data['hisse_sahipleri'] as $hisse_sahibi) {
        $ad = trim($hisse_sahibi['ad']);
        $soyad = trim($hisse_sahibi['soyad']);
        
        if(empty($ad) || empty($soyad)) {
            continue;
        }
        
        // Veritabanında aynı ad-soyad kombinasyonunu ara
        $stmt = $db->prepare("SELECT ad, soyad, created_at FROM hisseler WHERE LOWER(TRIM(ad)) = LOWER(?) AND LOWER(TRIM(soyad)) = LOWER(?) LIMIT 1");
        $stmt->execute([$ad, $soyad]);
        $kayit = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if($kayit) {
            $tarih = date('d-m-Y', strtotime($kayit['created_at']));
            $bulunan_kayitlar[] = [
                'ad' => $kayit['ad'],
                'soyad' => $kayit['soyad'],
                'tarih' => $tarih,
                'tam_isim' => $kayit['ad'] . ' ' . $kayit['soyad']
            ];
        }
    }
    
    echo json_encode([
        'success' => true,
        'bulunan_kayitlar' => $bulunan_kayitlar
    ]);
    
} catch(Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'mesaj' => 'Bir hata oluştu: ' . $e->getMessage()
    ]);
}
?>
