<?php
ob_start(); // Çıktıyı tamponla
error_reporting(E_ALL);
ini_set('display_errors', 1);
include_once $_SERVER['DOCUMENT_ROOT'] . '/Sabitler/GenelAyarlar.php';
include_once ANA_DIZIN . '/Sabitler/VT.php';
include_once ANA_DIZIN . '/Sabitler/Fonksiyonlar.php';
include_once ANA_DIZIN . '/islemler/mailgonder.php';


header('Content-Type: application/json');



$database = new Database();
$db = $database->getConnection();

if(!$db) {
    http_response_code(500);
    die(hataYaz('Veritabanı bağlantı hatası'));
}

$data = json_decode(file_get_contents('php://input'), true);

try {
    $db->beginTransaction();

    $hisse_sayisi = intval($data['hisse_sayisi']);
    $hisse_tutari = $data['hisse_tutari'];
    $hisse_index = 0;

    // 1. Tüm grupları ve mevcut hisse sayılarını çek
    $gruplar = [];
    $stmt = $db->query("SELECT g.grup_id, g.grup_turu, COUNT(h.hisse_id) as dolu, 7-COUNT(h.hisse_id) as bos
                        FROM gruplar g
                        LEFT JOIN hisseler h ON g.grup_id = h.grup_id
                        GROUP BY g.grup_id, g.grup_turu
                        ORDER BY g.grup_id ASC");
    while($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $gruplar[] = $row;
    }

    // Debug bilgisi
    error_log("Gelen kurban türü: " . $data['kurban_turu']);
    error_log("Mevcut gruplar: " . print_r($gruplar, true));

    // 1- Bir grupta yeterli boşluk varsa → hepsi o gruba eklensin.
    $uygun_grup = null;
    
    // Önce kurban türüne göre uygun grupları filtrele
    $uygun_gruplar = array_filter($gruplar, function($grup) use ($data) {
        $kurban_turu = trim($data['kurban_turu']);
        $grup_turu = trim($grup['grup_turu']);
        
        error_log("Kontrol edilen grup: " . $grup_turu . " - Kurban türü: " . $kurban_turu);
        
        // Vacip kontrolü
        if (strpos($kurban_turu, "Vacip") !== false) {
            return $grup_turu == "Vacip";
        } else {
            return $grup_turu == "Diğer";
        }
    });

    error_log("Filtrelenmiş gruplar: " . print_r($uygun_gruplar, true));

    // Filtrelenmiş gruplar arasından yeterli boşluğu olanı bul
    foreach($uygun_gruplar as $grup) {
        if($grup['bos'] >= $hisse_sayisi) {
            $uygun_grup = $grup['grup_id'];
            error_log("Seçilen grup ID: " . $uygun_grup);
            break;
        }
    }

    // INSERT sorgusunu güncelle - hisse_sira kolonunu ekle
    $stmt_insert = $db->prepare("INSERT INTO hisseler (
        grup_id, kurban_turu, ad, soyad, telefon,
        basvuran_ad, basvuran_soyad, basvuran_telefon,
        hisse_tutari, odeme_tipi, hisse_sira
    ) VALUES (
        :grup_id, :kurban_turu, :ad, :soyad, :telefon,
        :basvuran_ad, :basvuran_soyad, :basvuran_telefon,
        :hisse_tutari, :odeme_tipi, :hisse_sira
    )");

    if($uygun_grup) {
        // Mevcut hisse sayısını kontrol et ve sıra numarasını belirle
        $stmt_sira = $db->prepare("SELECT COUNT(*) as mevcut_hisse FROM hisseler WHERE grup_id = :grup_id");
        $stmt_sira->execute(['grup_id' => $uygun_grup]);
        $mevcut_hisse = $stmt_sira->fetch(PDO::FETCH_ASSOC)['mevcut_hisse'];
        $hisse_sira_baslangic = $mevcut_hisse + 1;
        
        // Tüm hisseleri bu gruba ekle
        $sira = $hisse_sira_baslangic;
        foreach($data['hisse_sahipleri'] as $hisse) {
            $stmt_insert->execute([
                'grup_id' => $uygun_grup,
                'kurban_turu' => $data['kurban_turu'],
                'ad' => temizle($hisse['ad']),
                'soyad' => temizle($hisse['soyad']),
                'telefon' => telefonFormati($hisse['telefon']),
                'basvuran_ad' => temizle($data['basvuran']['ad']),
                'basvuran_soyad' => temizle($data['basvuran']['soyad']),
                'basvuran_telefon' => telefonFormati($data['basvuran']['telefon']),
                'hisse_tutari' => $hisse_tutari,
                'odeme_tipi' => $data['odeme_tipi'],
                'hisse_sira' => $sira
            ]);
            $sira++;
        }
        // Grup dolduysa güncelle
        $stmt = $db->prepare("SELECT COUNT(*) as toplam FROM hisseler WHERE grup_id = :grup_id");
        $stmt->execute(['grup_id' => $uygun_grup]);
        $toplam = $stmt->fetch(PDO::FETCH_ASSOC)['toplam'];
        if($toplam >= 7) {
            $db->prepare("UPDATE gruplar SET grup_durumu = 'DOLU' WHERE grup_id = :grup_id")
                ->execute(['grup_id' => $uygun_grup]);
        }
    } else {
        // 2- Mevcut gruplardaki boşluklara hisseleri sırayla dağıt
        foreach($gruplar as $grup) {
            // grup_turu kontrolü ekle
            $kurban_turu = trim($data['kurban_turu']);
            $grup_turu = trim($grup['grup_turu']);
            if ((strpos($kurban_turu, "Vacip") !== false && $grup_turu != "Vacip") ||
                (strpos($kurban_turu, "Vacip") === false && $grup_turu != "Diğer")) {
                continue;
            }
            $bos = intval($grup['bos']);
            $grup_id = $grup['grup_id'];
            
            // Mevcut hisse sayısını kontrol et ve sıra numarasını belirle
            $stmt_sira = $db->prepare("SELECT COUNT(*) as mevcut_hisse FROM hisseler WHERE grup_id = :grup_id");
            $stmt_sira->execute(['grup_id' => $grup_id]);
            $mevcut_hisse = $stmt_sira->fetch(PDO::FETCH_ASSOC)['mevcut_hisse'];
            $hisse_sira_baslangic = $mevcut_hisse + 1;
            
            $sira = $hisse_sira_baslangic;
            for($i=0; $i<$bos && $hisse_index<$hisse_sayisi; $i++) {
                $hisse = $data['hisse_sahipleri'][$hisse_index];
                $stmt_insert->execute([
                    'grup_id' => $grup_id,
                    'kurban_turu' => $data['kurban_turu'],
                    'ad' => temizle($hisse['ad']),
                    'soyad' => temizle($hisse['soyad']),
                    'telefon' => telefonFormati($hisse['telefon']),
                    'basvuran_ad' => temizle($data['basvuran']['ad']),
                    'basvuran_soyad' => temizle($data['basvuran']['soyad']),
                    'basvuran_telefon' => telefonFormati($data['basvuran']['telefon']),
                    'hisse_tutari' => $hisse_tutari,
                    'odeme_tipi' => $data['odeme_tipi'],
                    'hisse_sira' => $sira
                ]);
                $hisse_index++;
                $sira++;
            }
            // Grup dolduysa güncelle
            $stmt2 = $db->prepare("SELECT COUNT(*) as toplam FROM hisseler WHERE grup_id = :grup_id");
            $stmt2->execute(['grup_id' => $grup_id]);
            $toplam = $stmt2->fetch(PDO::FETCH_ASSOC)['toplam'];
            if($toplam >= 7) {
                $db->prepare("UPDATE gruplar SET grup_durumu = 'DOLU' WHERE grup_id = :grup_id")
                    ->execute(['grup_id' => $grup_id]);
            }
            if($hisse_index >= $hisse_sayisi) break;
        }

        // 3- Hala hisse kaldıysa yeni grup aç ve kalan hisseleri oraya ekle
        while($hisse_index < $hisse_sayisi) {
            $grup_turu = (strpos($data['kurban_turu'], "Vacip") !== false) ? "Vacip" : "Diğer";
            $db->prepare("INSERT INTO gruplar (grup_durumu, grup_turu) VALUES ('BOS', :grup_turu)")
                ->execute(['grup_turu' => $grup_turu]);
            $yeni_grup_id = $db->lastInsertId();

            $kalan = min(7, $hisse_sayisi - $hisse_index);
            for($i=0; $i<$kalan; $i++) {
                $hisse = $data['hisse_sahipleri'][$hisse_index];
                $stmt_insert->execute([
                    'grup_id' => $yeni_grup_id,
                    'kurban_turu' => $data['kurban_turu'],
                    'ad' => temizle($hisse['ad']),
                    'soyad' => temizle($hisse['soyad']),
                    'telefon' => telefonFormati($hisse['telefon']),
                    'basvuran_ad' => temizle($data['basvuran']['ad']),
                    'basvuran_soyad' => temizle($data['basvuran']['soyad']),
                    'basvuran_telefon' => telefonFormati($data['basvuran']['telefon']),
                    'hisse_tutari' => $hisse_tutari,
                    'odeme_tipi' => $data['odeme_tipi'],
                    'hisse_sira' => $i + 1 // Yeni grup için 1'den başla
                ]);
                $hisse_index++;
            }
            // Eğer grup dolduysa güncelle
            if($kalan == 7) {
                $db->prepare("UPDATE gruplar SET grup_durumu = 'DOLU' WHERE grup_id = :grup_id")
                    ->execute(['grup_id' => $yeni_grup_id]);
            }
        }
    }

   
    $db->commit();

    // Kayıt bilgilerini içeren mail gönderimi
    kayitBilgisiMailGonder($data);

    echo json_encode([
        'success' => true,
        'mesaj' => 'Kayıt başarılı'
    ]);
    exit;
    

} catch(Exception $e) {
    $db->rollBack();
    http_response_code(500);
echo json_encode([
    'success' => false,
    'mesaj' => 'Bir hata oluştu.'
]);
exit;
}

ob_end_flush(); // Tamponu temizle

?>