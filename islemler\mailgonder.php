<?php
use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

// PHPMailer dosyalarını dahil edin (yolunu kendi projenize göre ayarlayın)
require_once ANA_DIZIN . '/Kutuphane/PHPMailer/src/Exception.php';
require_once ANA_DIZIN . '/Kutuphane/PHPMailer/src/PHPMailer.php';
require_once ANA_DIZIN . '/Kutuphane/PHPMailer/src/SMTP.php';

function kayitBilgisiMailGonder($data) {
    $mail = new PHPMailer(true);
    $mail->setLanguage('tr', ANA_DIZIN . '/Kutuphane/PHPMailer/language/');
    $mail->CharSet = 'UTF-8';
    try {
        // Sunucu ayarları
        $mail->isSMTP();
        $mail->Host       = 'mail.afrikakardesligi.org'; // SMTP sunucu adresiniz
        $mail->SMTPAuth   = true;
        $mail->Username   = '<EMAIL>'; // SMTP kullanıcı adı
        $mail->Password   = '20Bil25**'; // SMTP şifreniz
        $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
        $mail->Port       = 587;

        // Alıcı ve gönderen
        $mail->setFrom('<EMAIL>', 'Yeni Kurban Kaydı');
        $mail->addAddress('<EMAIL>'); // Bilgi maili
         //  $mail->addAddress('<EMAIL>'); // Bilgi maili

        // İçerik
        $mail->isHTML(true);
        $mail->Subject = htmlspecialchars($data['basvuran']['ad']). " ". htmlspecialchars($data['basvuran']['soyad']);

        $mesaj = "<html><body style='font-family: Arial, sans-serif; background-color: #f7f7f7; margin:0; padding:0;'>";
        $mesaj .= "<div style='max-width:600px; margin:30px auto; background:#fff; border-radius:10px; box-shadow:0 2px 8px rgba(0,0,0,0.08); padding:30px;'>";
        $mesaj .= "<h3 style='color:#2d7a2d; '>Yeni Kurban Kaydı: </h3><h2 style='color:rgb(18, 49, 18);border-bottom:2px solid #e0e0e0; padding-bottom:2px;margin-top:0;'>". htmlspecialchars($data['basvuran']['ad']). " ". htmlspecialchars($data['basvuran']['soyad'])."</h2>";
        $mesaj .= "<table style='width:100%; border-collapse:collapse; margin-bottom:20px;'>";
        $mesaj .= "<tr><td style='padding:8px; font-weight:bold; color:#444;'>Kurban Türü:</td><td style='padding:8px;'>" . htmlspecialchars($data['kurban_turu']) . "</td></tr>";
        $mesaj .= "<tr style='background:#f2f9f2;'><td style='padding:8px; font-weight:bold; color:#444;'>Toplam Hisse Sayısı:</td><td style='padding:8px;'>" . htmlspecialchars($data['hisse_sayisi']) . "</td></tr>";
        $mesaj .= "<tr><td style='padding:8px; font-weight:bold; color:#444;'>Toplam Tutar:</td><td style='padding:8px;'>" . htmlspecialchars($data['hisse_tutari'] * $data['hisse_sayisi']) . " TL</td></tr>";
        $mesaj .= "<tr style='background:#f2f9f2;'><td style='padding:8px; font-weight:bold; color:#444;'>Ödeme Tipi:</td><td style='padding:8px;'>" . htmlspecialchars($data['odeme_tipi']) . "</td></tr>";
        $mesaj .= "</table>";

        $mesaj .= "<div style='background:#eaf6ea; border-radius:8px; padding:15px; margin-bottom:20px;'>";
        $mesaj .= "<h3 style='color:#2d7a2d; margin-top:0;'>Başvuran Bilgileri</h3>";
        $mesaj .= "<ul style='list-style:none; padding-left:0;'>";
        $mesaj .= "<li><strong>Ad:</strong> " . htmlspecialchars($data['basvuran']['ad']) . "</li>";
        $mesaj .= "<li><strong>Soyad:</strong> " . htmlspecialchars($data['basvuran']['soyad']) . "</li>";
        $mesaj .= "<li><strong>Telefon:</strong> " . htmlspecialchars($data['basvuran']['telefon']) . "</li>";
        $mesaj .= "</ul>";
        $mesaj .= "</div>";

        $mesaj .= "<div style='background:#f9f9f9; border-radius:8px; padding:15px;'>";
        $mesaj .= "<h3 style='color:#2d7a2d; margin-top:0;'>Hisse Sahipleri</h3>";
        if (isset($data['hisse_sahipleri']) && is_array($data['hisse_sahipleri'])) {
            foreach ($data['hisse_sahipleri'] as $index => $hisse) {
                $mesaj .= "<div style='border:1px solid #e0e0e0; border-radius:6px; margin-bottom:10px; padding:10px; background:#fff;'>";
                $mesaj .= "<h4 style='color:#3a3a3a; margin:0 0 8px 0;'>" . ($index + 1) . ". Hisse Sahibi</h4>";
                $mesaj .= "<ul style='list-style:none; padding-left:0; margin:0;'>";
                $mesaj .= "<li><strong>Ad:</strong> " . htmlspecialchars($hisse['ad']) . "</li>";
                $mesaj .= "<li><strong>Soyad:</strong> " . htmlspecialchars($hisse['soyad']) . "</li>";
                $mesaj .= "<li><strong>Telefon:</strong> " . htmlspecialchars($hisse['telefon']) . "</li>";
                $mesaj .= "</ul>";
                $mesaj .= "</div>";
            }
        }

        $mesaj .= "<div style='background:#eaf6ea; border-radius:8px; padding:15px; margin-bottom:20px;text-align:center;'>";
        $mesaj .= "<h4 style='color:#315e31; margin-top:0;'>Vekalet Onayı</h4>";
        $mesaj .= "<h3 style='color:#2d7a2d; margin-top:0;'>Vekatimi Verdim.</h3>";
        $mesaj .= "<span style='padding-top:0;'>" . htmlspecialchars($data['basvuran']['ad']) ." " .htmlspecialchars($data['basvuran']['soyad']) . "</span>";
        $mesaj .= "</div>";

        $mesaj .= "</div>";
        $mesaj .= "<div style='text-align:center; color:#aaa; font-size:12px; margin-top:30px;'>Kurban Takip Sistemi | Afrika Kardeşliği</div>";
        $mesaj .= "</div></body></html>";

        $mail->Body = $mesaj;

        $mail->send();
        return true;
    } catch (Exception $e) {
        error_log('Mail gönderilemedi. Hata: ' . $mail->ErrorInfo);
        return false;
    }
}
?>
